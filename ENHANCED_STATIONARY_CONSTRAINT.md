# 基于IMU的强化静止约束解决方案

## 🚨 问题描述

**现象**: 设备静止时，速度又开始"跑"了，无法稳定在零附近
**原因**: 当前的静止检测和约束机制不够强，无法有效抑制静止状态下的速度漂移

## 🎯 解决策略

### 核心思路
通过**多层次的IMU静止检测**和**分级约束机制**，实现强力的静止约束，确保静止时速度稳定在零附近。

## 🔧 强化约束机制

### 1. 多层次静止检测阈值

#### **超严格静止** (Ultra Stationary)
```c
bool ultra_stationary = (acc_magnitude < 0.1) && (gyro_magnitude < 0.015);
```
- **加速度**: < 0.1 m/s² (约0.01g)
- **角速度**: < 0.015 rad/s (约0.86°/s)
- **触发条件**: 设备几乎完全静止

#### **严格静止** (Strict Stationary)  
```c
bool strict_stationary = (acc_magnitude < 0.15) && (gyro_magnitude < 0.025);
```
- **加速度**: < 0.15 m/s² (约0.015g)
- **角速度**: < 0.025 rad/s (约1.43°/s)
- **触发条件**: 设备基本静止

#### **中等静止** (Moderate Stationary)
```c
bool moderate_stationary = (acc_magnitude < 0.25) && (gyro_magnitude < 0.04);
```
- **加速度**: < 0.25 m/s² (约0.025g)
- **角速度**: < 0.04 rad/s (约2.29°/s)
- **触发条件**: 设备相对静止

### 2. 分级约束策略

#### **超严格静止约束**
```c
if(ultra_stationary) {
    // 立即强制约束，防止速度"跑"
    if(vel_norm > 0.001) {
        double ultra_decay = 0.3; // 每个周期衰减70%
        vn[0] *= ultra_decay; vn[1] *= ultra_decay; vn[2] *= ultra_decay;
    }
    // 直接置零
    vn[0] = vn[1] = vn[2] = 0.0;
}
```

#### **严格静止约束**
```c
if(strict_stationary && ZUPT_flag == RETURN_SUCESS) {
    if(vel_norm > 0.005) {
        double strong_decay = 0.5; // 每个周期衰减50%
        vn[0] *= strong_decay; vn[1] *= strong_decay; vn[2] *= strong_decay;
    }
    if(vel_norm < 0.002) {
        vn[0] = vn[1] = vn[2] = 0.0;
    }
}
```

#### **中等静止约束**
```c
if(moderate_stationary && current_vel_mag < 0.2) {
    if(current_vel_mag > 0.02) {
        double moderate_decay = 0.85; // 每个周期衰减15%
        vn[0] *= moderate_decay; vn[1] *= moderate_decay; vn[2] *= moderate_decay;
    }
    if(current_vel_mag < 0.01) {
        vn[0] = vn[1] = vn[2] = 0.0;
    }
}
```

## 📍 实现位置

### 1. 应用层强化 (nav_app.c)

#### **静止检测增强**
```c
// 连续确认机制
static int stationary_confirm_counter = 0;
static int strong_stationary_counter = 0;

if(strict_stationary) {
    stationary_confirm_counter++;
    strong_stationary_counter++;
    
    // 立即启用强静止约束
    if(stationary_confirm_counter >= 10) { // 连续10个周期确认
        NAV_Data_Full.ZUPT_flag = RETURN_SUCESS;
        
        // 直接速度约束 - 防止速度"跑"
        if(current_vel_mag > 0.01) {
            double strong_decay = 0.7; // 每个周期衰减30%
            NAV_Data_Full.SINS.vn[0] *= strong_decay;
            NAV_Data_Full.SINS.vn[1] *= strong_decay;
            NAV_Data_Full.SINS.vn[2] *= strong_decay;
        }
        
        // 极小速度直接置零
        if(current_vel_mag < 0.005) {
            NAV_Data_Full.SINS.vn[0] = 0.0;
            NAV_Data_Full.SINS.vn[1] = 0.0;
            NAV_Data_Full.SINS.vn[2] = 0.0;
        }
    }
}
```

### 2. 卡尔曼滤波层强化 (nav_kf.c)

#### **超严格约束**
```c
if(ultra_stationary) {
    // 超严格静止：立即强制约束，防止速度"跑"
    if(vel_norm > 0.001) {
        double ultra_decay = 0.3; // 每个周期衰减70%
        NAV_Data_Full_p->SINS.vn[0] *= ultra_decay;
        NAV_Data_Full_p->SINS.vn[1] *= ultra_decay;
        NAV_Data_Full_p->SINS.vn[2] *= ultra_decay;
    }
    // 极小速度直接置零
    NAV_Data_Full_p->SINS.vn[0] = 0.0;
    NAV_Data_Full_p->SINS.vn[1] = 0.0;
    NAV_Data_Full_p->SINS.vn[2] = 0.0;
}
```

## 📊 约束强度对比

| 静止级别 | 加速度阈值 | 角速度阈值 | 衰减因子 | 置零阈值 | 约束强度 |
|----------|------------|------------|----------|----------|----------|
| **超严格** | <0.1 m/s² | <0.015 rad/s | 0.3 (衰减70%) | 直接置零 | 极强 |
| **严格** | <0.15 m/s² | <0.025 rad/s | 0.5 (衰减50%) | <0.002 m/s | 强 |
| **中等** | <0.25 m/s² | <0.04 rad/s | 0.85 (衰减15%) | <0.01 m/s | 中等 |
| **运动** | >0.5 m/s² | >0.08 rad/s | 仅发散保护 | 无置零 | 轻 |

## 🎯 关键改进点

### 1. **立即响应机制**
- 检测到超严格静止时，**立即**应用强约束
- 不等待长时间确认，防止速度继续"跑"

### 2. **直接置零策略**
- 超严格静止时，**直接将速度置零**
- 不依赖渐进收敛，立即停止漂移

### 3. **多层次保护**
- 三个层次的静止检测，确保不遗漏任何静止状态
- 分级约束，避免过度约束运动状态

### 4. **连续确认优化**
- 严格静止只需10个周期确认（0.05秒）
- 超严格静止立即生效，无需确认

## 🧪 预期效果

### **静止状态**
- **检测时间**: 0.05秒内检测到静止
- **收敛时间**: 0.1秒内速度收敛到零附近
- **稳定性**: 速度稳定保持在±0.001 m/s以内
- **无漂移**: 长期静止不会出现速度"跑"的现象

### **动态状态**
- **响应性**: 运动检测不受影响
- **跟踪性**: 正常运动时保持良好的跟踪性能
- **过渡性**: 静止到运动的转换平滑

## 🔍 调试验证

### 1. **静止测试**
```c
// 添加调试输出
printf("IMU: acc=%.4f, gyro=%.4f\n", acc_magnitude, gyro_magnitude);
printf("Vel: %.6f, Status: %s\n", current_vel_mag, 
       ultra_stationary ? "ULTRA_STATIC" : 
       strict_stationary ? "STRICT_STATIC" : 
       moderate_stationary ? "MODERATE_STATIC" : "MOVING");
```

### 2. **约束效果验证**
```c
// 记录约束前后的速度变化
printf("Before constraint: vn=[%.6f, %.6f, %.6f]\n", vn[0], vn[1], vn[2]);
// 应用约束
printf("After constraint: vn=[%.6f, %.6f, %.6f]\n", vn[0], vn[1], vn[2]);
```

## 💡 使用建议

### 1. **阈值调整**
- 如果仍有轻微漂移，可进一步降低静止检测阈值
- 如果误判运动为静止，可适当放宽阈值

### 2. **衰减因子调整**
- 如果收敛太慢，可降低衰减因子（增强约束）
- 如果过度约束，可提高衰减因子（减弱约束）

### 3. **置零阈值调整**
- 根据实际精度要求调整置零阈值
- 高精度应用可设置更小的置零阈值

这个强化的静止约束机制通过**多层次检测**和**分级约束**，能够有效解决静止时速度"跑"的问题，确保设备在真正静止时速度稳定在零附近！
