# 轮速模块完整分析

## 📁 轮速模块文件结构

### 主要文件
1. **NAV/nav_ods.c** - 轮速数据处理和标定算法
2. **bsp/src/bsp_can.c** - CAN总线轮速数据接收
3. **Source/inc/can_data.h** - 轮速数据结构定义
4. **Protocol/computerFrameParse.c** - 轮速数据状态监控

## 🔧 核心功能模块

### 1. 轮速数据结构 (can_data.h)

<augment_code_snippet path="Source/inc/can_data.h" mode="EXCERPT">
````c
typedef struct can_data_t
{
    float timestamp;                    /* 时间戳, 单位:s , 精度：0.0001*/
    float WheelSpeed_Front_Left;        /* 轮速 左前, 单位: m/s, 精度：待定*/
    float WheelSpeed_Front_Right;       /* 轮速 右前, 单位: m/s, 精度：待定*/
    float WheelSpeed_Back_Left;         /* 轮速 左后, 单位: m/s, 精度：待定*/
    float WheelSpeed_Back_Right;        /* 轮速 右后, 单位: m/s, 精度：待定*/
    float WheelSteer;                   /* 方向盘, 单位: °, 精度：待定*/
    float OdoPulse_1;                   /* 里程计脉冲, 单位:  个数, 精度: 待定 */
    float OdoPulse_2;                   /* 里程计脉冲, 单位:  个数, 精度: 待定 */
    uint8_t Gear;                       /* 汽车档位 */
}CcanDataTypeDef;
````
</augment_code_snippet>

### 2. 轮速数据获取 (Get_ODS_Data)

<augment_code_snippet path="NAV/nav_ods.c" mode="EXCERPT">
````c
void Get_ODS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
    double GPSvel=0.0;
    //确认是否有can信息
    NAV_Data_Full_p->ODS.ods_flag = CombineData_p->canInfo.flag;
    NAV_Data_Full_p->ODS.ods_caculat_flag = RETURN_FAIL;
    NAV_Data_Full_p->ODS.ods_W_flag = RETURN_FAIL;
    NAV_Data_Full_p->ODS.ods_normal_flag = RETURN_SUCESS;
    
    if(NAV_Data_Full_p->ODS.ods_flag) //*********轮速接收****
    {
        NAV_Data_Full_p->ODS.counter_old = NAV_Data_Full_p->ODS.counter;
        if(NAV_Data_Full_p->ODS.counter_old != CombineData_p->canInfo.counter)
        {
            //counter
            NAV_Data_Full_p->ODS.counter = CombineData_p->canInfo.counter;
            //data
            NAV_Data_Full_p->ODS.WheelSpeed_Front_Left = CombineData_p->canInfo.data.WheelSpeed_Front_Left;
            NAV_Data_Full_p->ODS.WheelSpeed_Front_Right = CombineData_p->canInfo.data.WheelSpeed_Front_Right;
            NAV_Data_Full_p->ODS.WheelSpeed_Back_Left = CombineData_p->canInfo.data.WheelSpeed_Back_Left;
            NAV_Data_Full_p->ODS.WheelSpeed_Back_Right = CombineData_p->canInfo.data.WheelSpeed_Back_Right;
            NAV_Data_Full_p->ODS.WheelSteer = CombineData_p->canInfo.data.WheelSteer;
            NAV_Data_Full_p->ODS.OdoPulse_1 = CombineData_p->canInfo.data.OdoPulse_1;
            NAV_Data_Full_p->ODS.OdoPulse_2 = CombineData_p->canInfo.data.OdoPulse_2;
            NAV_Data_Full_p->ODS.Gear = CombineData_p->canInfo.data.Gear;
        }
````
</augment_code_snippet>

### 3. 轮速优化算法 (WheelSpeedOptimize)

<augment_code_snippet path="NAV/nav_ods.c" mode="EXCERPT">
````c
double WheelSpeedOptimize(double v0,double v1,double v2,double v3,double scale_factor,unsigned char *pSpeed_valid_flag)
{
    double v_wheel_ave=0.0;
    
    //*pSpeed_valid_flag = RETURN_SUCESS;
    //****************2 BACKWARD WHEEL ONLY***************
    //v_wheel_ave=scale_factor*(v2+v3)/2;
    v_wheel_ave=(v2+v3)/2;
    return   v_wheel_ave;//scale_factor*v_wheel_ave;
    //*******************************
````
</augment_code_snippet>

**注意**：当前实现只使用后轮速度的平均值，前轮数据被忽略。

### 4. 轮速标定算法 (ODS_Angle_Estimation)

<augment_code_snippet path="NAV/nav_ods.c" mode="EXCERPT">
````c
void ODS_Angle_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
    // 轮速标定主要包含三个部分：
    // 1. 安装角估计 (SubKF)
    // 2. 航向角校正 (GPS heading)  
    // 3. 比例因子标定 (scale_factor)
    
    if(RETURN_SUCESS==NAV_Data_Full_p->ODS.ods_caculat_flag && fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave)>0.5)
    {
        double R_wheel_tmp=R_wheel_factor;
        if(NAV_Data_Full_p->TurnningFlag)
        {
            R_wheel_tmp=4*R_wheel_factor;
        }
        R_wheel_tmp = R_wheel_tmp / fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave)/ fabs(NAV_Data_Full_p->ODS.WheelSpeed_ave);
        NAV_Data_Full_p->ODS.scale_factor = mis_Zktmp[1]/NAV_Data_Full_p->ODS.WheelSpeed_ave;
        Scalar_KalmanFilte(&NAV_Data_Full_p->ODS.scale_factor_filte, NAV_Data_Full_p->ODS.scale_factor, &NAV_Data_Full_p->ODS.P_wheel_fact, 1.0*Q_wheel_factor, R_wheel_tmp,0.15);
    }
}
````
</augment_code_snippet>

### 5. CAN数据接收 (CAN0_RX0_IRQHandler)

<augment_code_snippet path="bsp/src/bsp_can.c" mode="EXCERPT">
````c
void CAN0_RX0_IRQHandler(void)
{
    can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
    
    // 根据不同车型解析轮速数据
    if((CAN_ID_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
        hCAN0.RecvdFlag = SET;
        g_front_detected = 1;
        hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
        hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
        
        combineData.canInfo.data.WheelSpeed_Front_Left = hINSData.CAN_Data.WheelSpeed_Front_Left; 
        combineData.canInfo.data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Right; 
        combineData.canInfo.counter++;
    }
}
````
</augment_code_snippet>

### 6. 轮速状态监控 (wheel_is_running)

<augment_code_snippet path="Protocol/computerFrameParse.c" mode="EXCERPT">
````c
void wheel_is_running(void)
{
    static uint32_t lastCanCounter = 0;
    static uint32_t canPeriod = 0;
    
    if(lastCanCounter == combineData.canInfo.counter)
    {
        canPeriod++;
    }
    else
    {
        canPeriod = 0;
    }
    
    //最多允许5个同值
    if(canPeriod<6)
        combineData.canInfo.flag = 1;
    else
        combineData.canInfo.flag = 0;
    
    lastCanCounter = combineData.canInfo.counter;
}
````
</augment_code_snippet>

## 🚗 支持的车型

系统支持多种车型的轮速数据格式：

1. **五菱MiniEV** (VEHICLE_TYPE_WuLing_MiniEV)
2. **比亚迪** (VEHICLE_TYPE_BYD) 
3. **一汽解放J7** (VEHICLE_TYPE_YiQiJieFang_J7)
4. **东风风神E70** (VEHICLE_TYPE_DongFengFengShen_E70)
5. **东风悦翔** (VEHICLE_TYPE_DongFeng_YueXiang)
6. **博雷顿** (VEHICLE_TYPE_BoLeiDun)
7. **AutoBots RoboMix** (VEHICLE_TYPE_AutoBots_RoboMix)

## 🔧 关键参数

### 标定参数
- **Q_wheel_factor**: 1.0e-10 (过程噪声)
- **R_wheel_factor**: 0.05*0.05 (观测噪声)
- **scale_factor**: 轮速比例因子，动态标定

### 数据处理参数
- **轮距L**: 1.29m (用于角速度计算)
- **单位转换**: KMPH2MPS (km/h转m/s)
- **有效性检查**: 最多允许5个相同计数值

## ❗ 发现的问题

### 1. 轮速优化算法简化
- 当前只使用后轮平均速度
- 前轮数据被完全忽略
- 缺少异常数据检测和过滤

### 2. 标定条件限制
- 需要速度>0.5m/s才进行标定
- 转弯时观测噪声增加4倍
- 标定收敛可能较慢

### 3. 数据质量检查不足
- 缺少四轮数据一致性检查
- 没有速度跳变检测
- 异常值过滤机制简单

## 💡 改进建议

### 1. 增强轮速优化算法
```c
double WheelSpeedOptimize_Enhanced(double v0, double v1, double v2, double v3, 
                                  double scale_factor, unsigned char *valid_flag)
{
    // 1. 数据有效性检查
    if(v0 < 0 || v1 < 0 || v2 < 0 || v3 < 0) {
        *valid_flag = 0;
        return 0.0;
    }
    
    // 2. 数据一致性检查
    double avg_speed = (v0 + v1 + v2 + v3) / 4.0;
    double max_diff = 0.0;
    max_diff = fmax(max_diff, fabs(v0 - avg_speed));
    max_diff = fmax(max_diff, fabs(v1 - avg_speed));
    max_diff = fmax(max_diff, fabs(v2 - avg_speed));
    max_diff = fmax(max_diff, fabs(v3 - avg_speed));
    
    // 如果差异过大，可能是数据异常
    if(max_diff > 0.5 * avg_speed && avg_speed > 1.0) {
        *valid_flag = 0;
        return 0.0;
    }
    
    *valid_flag = 1;
    return avg_speed * scale_factor;
}
```

### 2. 改进标定算法
- 降低标定速度阈值到0.2m/s
- 增加标定数据质量检查
- 实现自适应观测噪声调整

### 3. 增强异常检测
- 添加速度跳变检测
- 实现轮速数据时间戳检查
- 增加CAN通信质量监控

这个轮速模块是导航系统的重要组成部分，直接影响到车辆标定和导航精度。当前的实现相对简单，有较大的改进空间。
