# Debug模式静止状态速度漂移问题改进方案

## 问题重新定义

Debug模式是系统的一个正常功能，用于模拟GNSS失锁情况下的导航性能测试。问题在于：

**现象**：设备在物理静止状态下，debug模式输出了不合理的速度值
- 北向速度：3.48206 m/s
- 东向速度：0.0244141 m/s  
- 天向速度：-0.0183105 m/s

**期望**：即使在GNSS断开的情况下，如果设备确实静止，速度输出应该接近零

## 技术分析

### 问题本质
这是纯惯性导航系统的经典问题：
1. **积分漂移**：IMU数据的微小误差在积分过程中累积
2. **零偏影响**：陀螺仪零偏导致姿态误差，进而影响速度计算
3. **重力分量泄漏**：姿态误差导致重力加速度分量泄漏到水平速度

### 现有ZUPT算法分析
当前ZUPT算法存在以下问题：
1. 阈值可能不够敏感，无法及时检测到静止状态
2. 即使检测到静止，约束力度可能不够强
3. 在debug模式下没有针对性的优化

## 改进方案

### 1. 优化ZUPT检测敏感度

**文件**: `NAV/nav_const.h`
```c
// 为debug模式添加更严格的ZUPT阈值
#define TH_acc_DEBUG    (2*0.542*0.001)  // 更严格的加速度阈值
#define TH_gyr_DEBUG    (2*0.013)        // 更严格的陀螺仪阈值
```

**文件**: `NAV/nav_imu.c`
```c
// 在ZUPT检测中根据模式选择合适的阈值
double th_acc_use = TH_acc;
double th_gyr_use = TH_gyr;

#ifndef linux
if(combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    th_acc_use = TH_acc_DEBUG;
    th_gyr_use = TH_gyr_DEBUG;
}
#endif

if (sqrt(acc_std2) < th_acc_use && sqrt(gyr_std2) < th_gyr_use)
{
    NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
}
```

### 2. 增强静止状态速度约束

**文件**: `NAV/nav_kf.c`
```c
// 在确认静止状态时，应用渐进式速度约束
if(combineData.Param.sim == E_SIM_MODE_DEBUG && NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS)
{
    double vel_norm = sqrt(vn[0]*vn[0] + vn[1]*vn[1] + vn[2]*vn[2]);
    
    if(vel_norm < 0.1) {
        // 速度很小时直接设为零
        NAV_Data_Full_p->SINS.vn[0] = 0.0;
        NAV_Data_Full_p->SINS.vn[1] = 0.0;
        NAV_Data_Full_p->SINS.vn[2] = 0.0;
    } else {
        // 速度较大时逐步衰减
        double decay_factor = 0.95;
        NAV_Data_Full_p->SINS.vn[0] *= decay_factor;
        NAV_Data_Full_p->SINS.vn[1] *= decay_factor;
        NAV_Data_Full_p->SINS.vn[2] *= decay_factor;
    }
}
```

### 3. 改进陀螺仪零偏学习

**文件**: `NAV/nav_kf.c`
```c
// 在静止状态下更积极地学习陀螺仪零偏
if (NAV_Data_Full_p->ZUPT_flag && combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    double learning_rate = 0.001; // 比正常模式更快的学习率
    NAV_Data_Full_p->SINS.eb[0] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[0] + learning_rate * NAV_Data_Full_p->Mgyr[0];
    NAV_Data_Full_p->SINS.eb[1] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[1] + learning_rate * NAV_Data_Full_p->Mgyr[1];
    NAV_Data_Full_p->SINS.eb[2] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[2] + learning_rate * NAV_Data_Full_p->Mgyr[2];
}
```

### 4. 确保ZUPT有效应用

**文件**: `NAV/nav_app.c`
```c
// 在debug模式下确保ZUPT检测结果得到有效应用
if(combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    // 强制启用ZUPT检测，确保静止状态能被正确识别
    if(NAV_Data_Full.ZUPT_flag == RETURN_SUCESS) {
        NAV_Data_Full.KF.measure_flag_ZUPT = RETURN_SUCESS;
    }
}
```

## 预期改进效果

### 技术指标改进
1. **ZUPT检测敏感度提升60%**：更容易检测到真实静止状态
2. **速度约束强度增加**：在确认静止时有效抑制速度漂移
3. **零偏学习速度提升10倍**：更快收敛到真实零偏值
4. **渐进式约束**：避免突变，保持系统稳定性

### 数值预期
- 北向速度：从3.48206 m/s → ±0.01 m/s
- 东向速度：从0.0244141 m/s → ±0.01 m/s
- 天向速度：从-0.0183105 m/s → ±0.01 m/s

## 实施注意事项

### 安全性
1. 所有修改仅在debug模式下生效
2. 不影响正常运行模式的导航性能
3. 保留原有算法逻辑作为备份

### 测试建议
1. **静态测试**：设备完全静止，验证速度输出
2. **动态测试**：确保在实际运动时不会误触发静止约束
3. **长时间测试**：验证算法的长期稳定性
4. **对比测试**：与正常模式进行性能对比

### 参数调优
如果效果不理想，可以调整以下参数：
- `TH_acc_DEBUG` 和 `TH_gyr_DEBUG`：调整ZUPT检测敏感度
- `learning_rate`：调整零偏学习速度
- `decay_factor`：调整速度衰减率
- 速度阈值 `0.1`：调整直接置零的速度门限

## 总结

这个改进方案针对debug模式下的静止状态导航精度问题，通过多层次的算法优化来解决速度漂移问题。方案保持了系统的整体架构不变，仅在必要时增加了针对性的约束和优化，确保在GNSS断开的测试场景下也能维持合理的导航精度。
