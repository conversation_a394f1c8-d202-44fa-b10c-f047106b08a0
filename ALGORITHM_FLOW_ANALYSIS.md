# 导航算法流程详细分析

## 🔄 主算法执行流程

### 1. 系统启动流程
```
系统上电
    ↓
硬件初始化 (INS_Init)
    ↓
参数加载 (ReadParaFromFlash)
    ↓
算法初始化 (NAV_Data_Full.Nav_Status = E_NAV_STATUS_START)
    ↓
进入主循环 (while(1))
```

### 2. 主循环执行流程 (200Hz)
```c
while(1) {
    if(fpga_syn) {                    // FPGA同步信号
        fpga_syn = 0;
        
        // 1. 数据获取阶段
        get_fpgadata();               // 获取FPGA预处理的IMU数据
        wheel_is_running();           // 检查轮速计状态
        
        // 2. 数据有效性检查
        if(combineData.gnssInfo.gpssecond != 0) {
            NAV_function();           // 执行导航算法
        }
        
        // 3. 结果输出
        NAV_Output();                 // 输出导航结果
    }
}
```

## 🧮 NAV_function() 详细流程

### 数据获取阶段
```c
void NAV_function(void) {
    // 1. 参数和系统数据获取
    Get_Param_Data(&NAV_Data_Full, &combineData);
    
    // 2. 传感器数据获取
    Get_IMU_Data(&NAV_Data_Full, &combineData);      // IMU数据处理
    Get_GNSS_Data(&NAV_Data_Full, &combineData);     // GNSS数据处理  
    Get_ODS_Data(&NAV_Data_Full, &combineData);      // 轮速计数据处理
    
    // 3. 状态机执行
    switch(NAV_Data_Full.Nav_Status) {
        case E_NAV_STATUS_START:              // 系统启动
        case E_NAV_STATUS_SINS_KF_INITIAL:    // 初始化
        case E_NAV_STATUS_SYSTEM_STANDARD:    // 系统标定
        case E_NAV_STATUS_IN_NAV:             // 正常导航
        case E_NAV_STATUS_STOP:               // 停止导航
    }
}
```

### 状态机详细分析

#### 1. E_NAV_STATUS_START (系统启动)
```c
case E_NAV_STATUS_START: {
    // 参数初始化
    Param_Data_Init(&NAV_Data_Full);
    
    // 加载标定数据
    Load_Standard_Data(&NAV_Data_Full, &combineData);
    
    // 状态转换
    SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
}
```

#### 2. E_NAV_STATUS_SINS_KF_INITIAL (SINS/KF初始化)
```c
case E_NAV_STATUS_SINS_KF_INITIAL: {
    // SINS系统初始化
    SINS_Init(&NAV_Data_Full);
    
    // 检查初始化完成
    if(NAV_Data_Full.SINS.Init_flag == 1) {
        SetNavStatus(E_NAV_STATUS_IN_NAV);
    }
}
```

#### 3. E_NAV_STATUS_IN_NAV (正常导航)
```c
case E_NAV_STATUS_IN_NAV: {
    // 根据算法类型执行
    if(E_METHOD_TYPE_KALMAN == combineData.Param.methodType) {
        NavKalmanMode(g_NavIndex);    // 卡尔曼滤波模式
    } else {
        NavDRMode(g_NavIndex);        // 航位推算模式
    }
}
```

## 🎯 NavKalmanMode() 核心算法

### 算法执行顺序
```c
void NavKalmanMode(unsigned int testcount) {
    // 1. SINS机械编排
    SINS_Update(&NAV_Data_Full);
    
    // 2. 卡尔曼滤波更新
    KF_UP2(&NAV_Data_Full, &combineData);
}
```

### SINS_Update() 详细流程
```c
void SINS_Update(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 姿态更新
    att_update(NAV_Data_Full_p);
    
    // 2. 速度更新  
    vel_update(NAV_Data_Full_p);
    
    // 3. 位置更新
    pos_update(NAV_Data_Full_p);
    
    // 4. 重力和地球自转补偿
    earth_update(NAV_Data_Full_p);
}
```

#### 姿态更新算法
```c
void att_update(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 角速度补偿
    double wb[3];
    wb[0] = gyro[0] - bias_gyro[0];  // 补偿零偏
    wb[1] = gyro[1] - bias_gyro[1];
    wb[2] = gyro[2] - bias_gyro[2];
    
    // 2. 四元数更新
    quat_update(qnb, wb, dt);
    
    // 3. 转换为欧拉角
    qnb2att(qnb, att);
    
    // 4. 更新方向余弦矩阵
    qnb2Cnb(qnb, Cnb);
}
```

#### 速度更新算法
```c
void vel_update(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 加速度补偿
    double fb[3];
    fb[0] = acc[0] - bias_acc[0];    // 补偿零偏
    fb[1] = acc[1] - bias_acc[1];
    fb[2] = acc[2] - bias_acc[2];
    
    // 2. 坐标系转换 (机体系→导航系)
    double fn[3];
    Mat_Mul_Vec(Cnb, fb, fn);
    
    // 3. 重力补偿
    fn[2] += G0;
    
    // 4. 速度积分
    vn[0] += fn[0] * dt;
    vn[1] += fn[1] * dt;
    vn[2] += fn[2] * dt;
}
```

#### 位置更新算法
```c
void pos_update(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 位置积分
    pos[0] += vn[0] * dt;  // 纬度
    pos[1] += vn[1] * dt;  // 经度
    pos[2] += vn[2] * dt;  // 高度
    
    // 2. 地球曲率补偿
    double Rm = earth_radius_meridian(pos[0]);
    double Rn = earth_radius_normal(pos[0]);
    
    pos[0] += vn[0] / (Rm + pos[2]) * dt;
    pos[1] += vn[1] / ((Rn + pos[2]) * cos(pos[0])) * dt;
}
```

### KF_UP2() 卡尔曼滤波流程
```c
void KF_UP2(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p) {
    // 1. 预测步骤
    KF_Predict(NAV_Data_Full_p);
    
    // 2. 观测更新
    if(GPS_available) {
        KF_Update_GPS(NAV_Data_Full_p);      // GPS观测更新
    }
    
    if(ZUPT_detected) {
        KF_Update_ZUPT(NAV_Data_Full_p);     // ZUPT观测更新
    }
    
    if(Wheel_available) {
        KF_Update_Wheel(NAV_Data_Full_p);    // 轮速观测更新
    }
    
    // 3. 状态反馈
    State_Feedback(NAV_Data_Full_p);
}
```

#### 卡尔曼滤波预测
```c
void KF_Predict(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 状态转移矩阵构建
    Build_F_Matrix(F, NAV_Data_Full_p);
    
    // 2. 状态预测
    // X(k|k-1) = F * X(k-1|k-1)
    Mat_Mul(F, X, X_pred);
    
    // 3. 协方差预测  
    // P(k|k-1) = F * P(k-1|k-1) * F' + Q
    Mat_Mul(F, P, temp);
    Mat_Mul(temp, F_T, P_pred);
    Mat_Add(P_pred, Q, P_pred);
}
```

#### GPS观测更新
```c
void KF_Update_GPS(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. 观测矩阵构建
    Build_H_GPS(H);
    
    // 2. 观测残差计算
    Z[0] = GPS_pos[0] - SINS_pos[0];  // 位置残差
    Z[1] = GPS_pos[1] - SINS_pos[1];
    Z[2] = GPS_pos[2] - SINS_pos[2];
    Z[3] = GPS_vel[0] - SINS_vel[0];  // 速度残差
    Z[4] = GPS_vel[1] - SINS_vel[1];
    Z[5] = GPS_vel[2] - SINS_vel[2];
    
    // 3. 卡尔曼增益计算
    // K = P * H' * (H * P * H' + R)^(-1)
    Calculate_Kalman_Gain(K, P, H, R);
    
    // 4. 状态更新
    // X = X + K * Z
    Mat_Mul_Vec(K, Z, delta_X);
    Vec_Add(X, delta_X, X);
    
    // 5. 协方差更新
    // P = (I - K * H) * P
    Update_Covariance(P, K, H);
}
```

#### ZUPT观测更新
```c
void KF_Update_ZUPT(_NAV_Data_Full_t* NAV_Data_Full_p) {
    // 1. ZUPT观测 (速度为零)
    Z_ZUPT[0] = 0 - SINS_vel[0];
    Z_ZUPT[1] = 0 - SINS_vel[1]; 
    Z_ZUPT[2] = 0 - SINS_vel[2];
    
    // 2. 观测矩阵 (只约束速度)
    H_ZUPT = [0 I3x3 0 ...];  // 只有速度部分为单位矩阵
    
    // 3. 观测噪声 (很小，强约束)
    R_ZUPT = diag([0.02^2, 0.02^2, 0.02^2]);
    
    // 4. 标准卡尔曼更新
    Standard_KF_Update(Z_ZUPT, H_ZUPT, R_ZUPT);
}
```

## 🔧 关键算法参数

### 状态向量 (21维)
```c
X = [δP(3), δV(3), δΨ(3), εb(3), ∇b(3), Sg(3), Sa(3)]
```
- δP: 位置误差 (纬度、经度、高度)
- δV: 速度误差 (东、北、天)  
- δΨ: 姿态误差 (横滚、俯仰、航向)
- εb: 陀螺零偏 (X、Y、Z轴)
- ∇b: 加速度零偏 (X、Y、Z轴)
- Sg: 陀螺比例因子 (X、Y、Z轴)
- Sa: 加速度比例因子 (X、Y、Z轴)

### 过程噪声矩阵Q
```c
Q = diag([
    1e-6,  1e-6,  1e-6,    // 位置过程噪声
    1e-4,  1e-4,  1e-4,    // 速度过程噪声
    1e-6,  1e-6,  1e-6,    // 姿态过程噪声
    1e-10, 1e-10, 1e-10,   // 陀螺零偏噪声
    1e-8,  1e-8,  1e-8,    // 加速度零偏噪声
    1e-12, 1e-12, 1e-12,   // 陀螺比例因子噪声
    1e-10, 1e-10, 1e-10    // 加速度比例因子噪声
]);
```

### 观测噪声矩阵R
```c
// GPS观测噪声
R_GPS = diag([
    5^2,   5^2,   10^2,    // GPS位置噪声 (m)
    0.1^2, 0.1^2, 0.2^2    // GPS速度噪声 (m/s)
]);

// ZUPT观测噪声
R_ZUPT = diag([0.02^2, 0.02^2, 0.02^2]);  // 很小，强约束

// 轮速观测噪声
R_Wheel = 0.1^2;  // 轮速噪声 (m/s)
```

这个算法流程展示了一个完整的GNSS/INS紧组合导航系统的实现，采用了经典的卡尔曼滤波框架，支持多种观测源的融合。
