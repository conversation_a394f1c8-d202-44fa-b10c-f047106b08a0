# 光纤和MEMS Z轴比例因子配置指南

## 🎯 比例因子概述

系统中有两套比例因子配置机制：
1. **光纤陀螺Z轴比例因子** - 用于光纤陀螺数据校正
2. **MEMS陀螺比例因子** - 用于MEMS IMU数据校正

## 📍 光纤陀螺Z轴比例因子配置

### 1. 硬编码设置位置

#### **main.c 中的定义**
```c
// 文件: Source/src/main.c (第470行)
#define FOG_GYRO_FACTOR 575865

// 转台数据标定的数值 - 标度因素 (第464-467行)
// FOG50: 5号（SN:202303090001）：301350   6号（SN:202303090008）：294790
// FOG60: 4号（SN:202402010004）：577002   17号（SN:202402010003）：576260  
//        10号（SN:202402010001）：577014   6号（SN:202303090001）：575865
```

#### **数据处理中的应用**
```c
// 文件: Source/src/main.c (第657行)
gfog0 = (int)get_16bit_Int32((unsigned short *)&gpagedata.fog_x0) / 1.0;
combineData.ifogInfo.gyroGrp[2] = gfog0 / (FOG_GYRO_FACTOR * 1.0);
```

### 2. 动态比例因子设置

#### **fog_kz 参数设置**
```c
// 文件: Source/src/main.c (第958-962行)
// 转台数据标定的数值 - 跑车测试时只需一次标定即可写入flash
// FOG50: 5号：1.002349136   6号：1.003379875
// FOG60: 4号：1.002215466   17号：1.002177689   10号：0.99339657
//        6号：1.00197572
NAV_Data_Full.SubKF.fog_kz = 1.00197572;
```

#### **fog_kz 参数加载**
```c
// 文件: NAV/nav_imu.c (第100-105行)
if(fabs(combineData.Adj.FOG_kz) < 0.5) {
    combineData.Adj.FOG_kz = 1.00;  // 默认值，方便调试
}
NAV_Data_Full_p->SubKF.fog_kz = combineData.Adj.FOG_kz;
```

#### **fog_kz 参数应用**
```c
// 文件: NAV/nav_imu.c (第286行)
NAV_Data_Full_p->IMU.gyro_fog[2] = NAV_Data_Full_p->IMU.gyro_fog_raw[2] * NAV_Data_Full_p->SubKF.fog_kz;
```

## 📍 MEMS陀螺比例因子配置

### 1. 协议设置接口

#### **陀螺标定因数协议**
```c
// 文件: Protocol/SetParaBao.h (第61行)
#define SETPARA_TYPE0_FactorGyro    (0x35AA)  // 陀螺标定因数

// 数据结构定义 (第283-290行)
typedef struct _parabag_info_SetFactorGyro {
    double FactorDyroX;   // X轴比例因子
    double FactorDyroY;   // Y轴比例因子  
    double FactorDyroZ;   // Z轴比例因子
    unsigned char reserve[222];  // 预留
} parabag_info_SetFactorGyro;
```

#### **协议处理函数**
```c
// 文件: Protocol/SetParaBao.c (第1594-1625行)
void SetParaFactorGyro(p_dmauart_t pdmauart) {
    parabag_SetFactorGyro stFactorGyro;
    memcpy(&stFactorGyro, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);
    
    uint8_t crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stFactorGyro.ender.check) {
        stSetPara.FactorDyroX = stFactorGyro.info.FactorDyroX;
        stSetPara.FactorDyroY = stFactorGyro.info.FactorDyroY;
        stSetPara.FactorDyroZ = stFactorGyro.info.FactorDyroZ;  // Z轴比例因子
        stSendPara.info.BackFlag = 0x01;  // 设置成功
    }
}
```

### 2. 上位机设置接口

#### **computerFrameParse.c 中的处理**
```c
// 文件: Protocol/computerFrameParse.c (第360-385行)
case CMD_GYRO_ADJUST:  // 陀螺校准
{
    if(*pDat++ == 0) {  // 读取
        comm_cali_ehco_rsp(tCmd);
    } else {  // 写入
        fp = &caliData.gyroCali.S_ax;
        
        for(i = 0; i < 12; i++) {  // 12个参数包括比例因子
            u_f2u8.u8_val[0] = *pDat++;
            u_f2u8.u8_val[1] = *pDat++;
            u_f2u8.u8_val[2] = *pDat++;
            u_f2u8.u8_val[3] = *pDat++;
            *fp++ = u_f2u8.f_val;
        }
        comm_write_rsp(tCmd);
    }
    comm_saveCaliData();  // 保存到Flash
}
```

## 🔧 配置方法

### 方法1: 修改源码硬编码

#### **光纤陀螺比例因子**
```c
// 1. 修改 FOG_GYRO_FACTOR (main.c 第470行)
#define FOG_GYRO_FACTOR 575865  // 改为您的设备标定值

// 2. 修改 fog_kz 参数 (main.c 第962行)  
NAV_Data_Full.SubKF.fog_kz = 1.00197572;  // 改为您的精细标定值
```

#### **重新编译烧录**
```bash
# 修改代码后需要重新编译和烧录固件
make clean
make all
# 烧录到设备
```

### 方法2: 通过协议动态设置

#### **陀螺标定因数协议包格式**
```
帧头: 0xAA 0x55
长度: 0x##
类型: 0x35 0xAA (SETPARA_TYPE0_FactorGyro)
数据: 
  FactorDyroX (8字节 double)
  FactorDyroY (8字节 double)  
  FactorDyroZ (8字节 double)  // Z轴比例因子
  预留 (222字节)
校验: CRC8
帧尾: 0x55 0xAA
```

#### **上位机设置命令**
```c
// CMD_GYRO_ADJUST 命令格式
// 帧头: 0xFA 0x55
// 命令: CMD_GYRO_ADJUST
// 数据: 写入标志(1) + 12个float参数(48字节)
// 帧尾: 0x00 0xFF
```

### 方法3: 配置文件设置

#### **sins.conf 配置文件**
```ini
# 文件: NAV/sins.conf (第24-37行)
# 设备是否需要进行标定，0：需要标定；2：表示无需标定
Adj_Nav_Standard_flag=0

# 陀螺初始零偏，rad/s，转台标定获取
Adj_gyro_off=0.0,0.0,0.0

# 加计初始零偏,单位m/s2，转台标定获取  
Adj_acc_off=0.0,0.0,0.0

# 天线安装角精准误差，单位度
Adj_gnssheading_from_vehicle=0.0

# IMU与载体的y轴夹角
Adj_att_ods2_b_filte_2=0.0
```

## 📊 不同设备的标定值

### 光纤陀螺设备标定值

#### **FOG50系列**
```c
// 5号设备 (SN:202303090001)
#define FOG_GYRO_FACTOR 301350
NAV_Data_Full.SubKF.fog_kz = 1.002349136;

// 6号设备 (SN:202303090008)  
#define FOG_GYRO_FACTOR 294790
NAV_Data_Full.SubKF.fog_kz = 1.003379875;
```

#### **FOG60系列**
```c
// 4号设备 (SN:202402010004)
#define FOG_GYRO_FACTOR 577002  
NAV_Data_Full.SubKF.fog_kz = 1.002215466;

// 17号设备 (SN:202402010003)
#define FOG_GYRO_FACTOR 576260
NAV_Data_Full.SubKF.fog_kz = 1.002177689;

// 10号设备 (SN:202402010001)
#define FOG_GYRO_FACTOR 577014
NAV_Data_Full.SubKF.fog_kz = 0.99339657;

// 6号设备 (SN:202303090001) 
#define FOG_GYRO_FACTOR 575865
NAV_Data_Full.SubKF.fog_kz = 1.00197572;
```

## 💾 参数保存机制

### Flash存储
```c
// 参数保存到Flash
void comm_saveCaliData(void);

// 从Flash加载参数
void Load_Calib_Parms(_NAV_Data_Full_t* NAV_Data_Full_temp, CombineDataTypeDef* CombineData_p);
```

### 参数验证
```c
// fog_kz 参数有效性检查
if(fabs(combineData.Adj.FOG_kz) < 0.5) {
    combineData.Adj.FOG_kz = 1.00;  // 设置默认值
}
```

## 🔍 调试验证

### 1. 检查当前设置值
```c
// 在调试输出中查看当前比例因子
printf("FOG_GYRO_FACTOR: %d\n", FOG_GYRO_FACTOR);
printf("fog_kz: %f\n", NAV_Data_Full.SubKF.fog_kz);
```

### 2. 验证数据处理
```c
// 检查原始数据和校正后数据
printf("Raw gyro Z: %f\n", NAV_Data_Full_p->IMU.gyro_fog_raw[2]);
printf("Corrected gyro Z: %f\n", NAV_Data_Full_p->IMU.gyro_fog[2]);
```

## 📝 注意事项

1. **设备特异性**: 每个设备都有独特的标定值，不能通用
2. **两级校正**: FOG_GYRO_FACTOR用于粗校正，fog_kz用于精细校正
3. **参数保存**: 动态设置的参数需要保存到Flash才能永久生效
4. **系统重启**: 修改硬编码值需要重新编译烧录固件

根据您的具体需求选择合适的配置方法！
