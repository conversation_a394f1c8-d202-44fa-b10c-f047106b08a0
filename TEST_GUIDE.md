# Debug模式速度漂移修复 - 测试指导

## 修改总结

基于您反馈的测试结果（3分钟后速度仍然较大），我实施了更强有力的多层次约束机制：

### 关键修改点

1. **三层速度约束机制**：
   - 应用层：每10个周期检查约束
   - 算法层：每次ZUPT处理时约束  
   - 底层：每200个周期强制重置

2. **分级衰减策略**：
   - 微小速度(<0.02 m/s)：直接置零
   - 小速度(0.02-0.5 m/s)：强力衰减(×0.8)
   - 大速度(>0.5 m/s)：超强衰减(×0.5)

3. **调整ZUPT阈值**：
   - 从严格60%调整为严格40%，提高触发概率

## 测试步骤

### 第一步：编译和烧录
1. 编译修改后的代码
2. 烧录到设备
3. 确保设备处于完全静止状态

### 第二步：启动测试
1. 启动设备，等待正常导航
2. 进入debug模式，断开GNSS
3. 开始记录速度数据

### 第三步：观察收敛过程
预期的收敛过程：

| 时间 | 预期速度范围 | 关键观察点 |
|------|-------------|-----------|
| 0-30秒 | 快速衰减 | 速度应该明显下降 |
| 30秒-2分钟 | < 0.5 m/s | 进入中等速度衰减模式 |
| 2-5分钟 | < 0.1 m/s | 进入精细约束模式 |
| 5分钟后 | < 0.05 m/s | 稳定在目标范围 |

### 第四步：记录关键数据
请记录以下数据：
1. **每30秒的速度值**：东向、北向、天向
2. **速度幅值**：sqrt(东向² + 北向² + 天向²)
3. **是否观察到明显的衰减趋势**

## 预期测试结果

### 成功指标
- **1分钟内**：速度幅值应该下降到1 m/s以下
- **3分钟内**：速度幅值应该下降到0.2 m/s以下  
- **5分钟内**：速度幅值应该稳定在0.05 m/s以下

### 如果仍然不收敛

如果5分钟后速度仍然大于0.2 m/s，请尝试以下调整：

#### 方案A：增强约束强度
修改 `NAV/nav_kf.c` 中的衰减因子：
```c
// 将现有的衰减因子改为更强
double decay_factor = 0.3; // 原来是0.5
double decay_factor = 0.6; // 原来是0.8
```

#### 方案B：缩短约束周期
修改 `NAV/nav_app.c` 中的约束周期：
```c
if(debug_force_counter >= 5) { // 原来是10
```

修改 `NAV/nav_kf.c` 中的重置周期：
```c
if(debug_reset_counter >= 100) { // 原来是200
```

#### 方案C：降低速度阈值
修改各处的速度阈值：
```c
if(vel_mag > 0.2) { // 原来是0.5
if(vel_norm < 0.01) { // 原来是0.02
if(vel_total > 0.05) { // 原来是0.1
```

## 调试信息

如果需要更详细的调试信息，可以在代码中添加打印语句：

### 在nav_app.c中添加：
```c
// 在约束执行时添加
printf("Debug constraint: vel_mag=%.4f, decay applied\n", vel_mag);
```

### 在nav_kf.c中添加：
```c
// 在ZUPT约束时添加
printf("ZUPT constraint: vel_norm=%.4f, factor=%.2f\n", vel_norm, decay_factor);

// 在定期重置时添加
printf("Periodic reset: vel_total=%.4f, scale=%.3f\n", vel_total, scale);
```

## 理论分析

### 为什么之前的方案效果有限
1. **单一约束机制**：仅依赖ZUPT可能不够
2. **约束强度不足**：衰减因子太温和
3. **约束频率不够**：约束间隔太长

### 新方案的优势
1. **多重保险**：三层约束确保不会失效
2. **渐进收敛**：分级处理避免系统震荡
3. **强制兜底**：定期重置确保最终收敛

## 联系方式

如果测试过程中遇到问题，请提供：
1. **具体的速度数值变化过程**
2. **收敛时间和最终稳定值**
3. **是否观察到明显的约束效果**

这将帮助我进一步优化约束参数，确保达到预期效果。

## 总结

这个增强版方案采用了"多重保险 + 分级约束"的策略，理论上应该能够在5分钟内将速度控制在0.05 m/s以内。如果仍有问题，我们可以根据实际测试结果进一步调整参数。
