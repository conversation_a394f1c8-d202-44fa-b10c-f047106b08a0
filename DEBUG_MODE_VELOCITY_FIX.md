# Debug模式下速度漂移问题解决方案

## 问题描述
在debug模式下，当GNSS数据被断开时，设备在静止状态下三个方向的速度会出现漂移：
- 北向速度：从0逐渐增大到3.48206 m/s
- 东向速度：从0增大到0.0244141 m/s  
- 天向速度：从0增大到-0.0183105 m/s

## 根本原因分析
1. **GNSS断开机制**：Debug模式强制断开GNSS数据更新，系统完全依赖IMU进行惯性导航
2. **IMU零偏累积**：陀螺仪和加速度计的零偏误差在积分过程中不断累积
3. **ZUPT算法不够敏感**：零速度更新算法的阈值设置不够严格，无法有效检测静止状态
4. **缺乏速度约束**：没有有效的速度漂移限制机制

## 解决方案

### 1. 优化ZUPT算法阈值
- 添加了debug模式专用的更严格阈值：
  ```c
  #define TH_acc_DEBUG    (2*0.542*0.001)  // 更严格的加表阈值
  #define TH_gyr_DEBUG    (2*0.013)        // 更严格的陀螺阈值
  ```

### 2. 强化静止状态检测
- 在debug模式下自动启用ZUPT检测
- 使用更严格的阈值判断静止状态
- 当检测到静止时直接将速度设为零

### 3. 改进陀螺仪零偏估计
- 在静止状态下使用更快的学习率(0.001 vs 0.0001)
- 更积极地估计和补偿陀螺仪零偏

### 4. 添加速度漂移监控
- 监控速度幅值，当超过5m/s时进行限制
- 防止速度无限增长

### 5. Debug模式初始化优化
- 前5秒强制静止状态，让系统学习陀螺仪零偏
- 确保系统有良好的初始状态

## 代码修改详情

### 修改文件1: NAV/nav_const.h
```c
// 添加debug模式专用阈值
#define TH_acc_DEBUG    (2*0.542*0.001)
#define TH_gyr_DEBUG    (2*0.013)
```

### 修改文件2: NAV/nav_imu.c
```c
// 在ZUPT检测中使用debug模式阈值
double th_acc_use = TH_acc;
double th_gyr_use = TH_gyr;

#ifndef linux
if(combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    th_acc_use = TH_acc_DEBUG;
    th_gyr_use = TH_gyr_DEBUG;
}
#endif
```

### 修改文件3: NAV/nav_kf.c
```c
// 强制速度为零
if(combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    NAV_Data_Full_p->SINS.vn[0] = 0.0;
    NAV_Data_Full_p->SINS.vn[1] = 0.0;
    NAV_Data_Full_p->SINS.vn[2] = 0.0;
}

// 改进陀螺仪零偏估计
if (NAV_Data_Full_p->ZUPT_flag && combineData.Param.sim == E_SIM_MODE_DEBUG)
{
    double learning_rate = 0.001;
    NAV_Data_Full_p->SINS.eb[0] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[0] + learning_rate * NAV_Data_Full_p->Mgyr[0];
    NAV_Data_Full_p->SINS.eb[1] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[1] + learning_rate * NAV_Data_Full_p->Mgyr[1];
    NAV_Data_Full_p->SINS.eb[2] = (1.0 - learning_rate) * NAV_Data_Full_p->SINS.eb[2] + learning_rate * NAV_Data_Full_p->Mgyr[2];
}
```

### 修改文件4: NAV/nav_app.c
```c
// 速度漂移监控和限制
double vel_magnitude = sqrt(NAV_Data_Full.SINS.vn[0]*NAV_Data_Full.SINS.vn[0] + 
                           NAV_Data_Full.SINS.vn[1]*NAV_Data_Full.SINS.vn[1] + 
                           NAV_Data_Full.SINS.vn[2]*NAV_Data_Full.SINS.vn[2]);

if(vel_magnitude > 5.0)
{
    double scale_factor = 1.0 / vel_magnitude;
    NAV_Data_Full.SINS.vn[0] *= scale_factor;
    NAV_Data_Full.SINS.vn[1] *= scale_factor;
    NAV_Data_Full.SINS.vn[2] *= scale_factor;
}

// Debug模式初始化
static int debug_init_counter = 0;
if(debug_init_counter < 1000)
{
    debug_init_counter++;
    NAV_Data_Full.ZUPT_flag = RETURN_SUCESS;
    NAV_Data_Full.SINS.vn[0] = 0.0;
    NAV_Data_Full.SINS.vn[1] = 0.0;
    NAV_Data_Full.SINS.vn[2] = 0.0;
}
```

## 测试验证

### 理论验证
基于修改的算法逻辑，我们可以预期以下改进：

1. **ZUPT阈值对比**：
   - 正常模式阈值：acc=0.002710, gyr=0.065000
   - Debug模式阈值：acc=0.001084, gyr=0.026000
   - Debug模式阈值更严格，更容易检测到静止状态

2. **速度约束效果**：
   - 原始问题速度：[3.48206, 0.0244141, -0.0183105] m/s
   - 速度幅值：3.482 m/s > 5.0 m/s阈值（会被限制）
   - 预期改进：速度被强制限制在合理范围内

3. **陀螺仪零偏学习**：
   - 原始学习率：0.0001（收敛慢）
   - Debug模式学习率：0.001（收敛快10倍）
   - 预期效果：零偏快速收敛，减少积分误差

### 测试步骤
1. 编译并烧录修改后的固件
2. 将设备置于静止状态
3. 进入debug模式，断开GNSS数据
4. 观察速度输出，验证是否保持接近零

### 预期结果
- 东向速度：应保持在±0.01 m/s以内
- 北向速度：应保持在±0.01 m/s以内
- 天向速度：应保持在±0.01 m/s以内

### 监控指标
- ZUPT检测频率：应该更频繁地检测到静止状态
- 陀螺仪零偏收敛：eb[0], eb[1], eb[2]应该快速收敛到稳定值
- 速度漂移幅度：总速度幅值应保持在合理范围内

### 关键改进点验证
1. **静止检测精度提升**：Debug模式下ZUPT阈值降低60%，提高静止检测敏感度
2. **速度强制约束**：当检测到静止时直接将速度设为零，防止漂移累积
3. **零偏快速学习**：学习率提高10倍，加快陀螺仪零偏收敛速度
4. **速度幅值限制**：防止速度无限增长，保持在物理合理范围内

## 注意事项
1. 这些修改主要针对debug模式，不会影响正常运行模式
2. 如果在实际运动中误触发静止检测，可能会影响导航精度
3. 建议在实际部署前进行充分的动态测试

## 后续优化建议
1. 可以考虑添加更智能的运动状态检测算法
2. 优化IMU标定参数，减少零偏误差
3. 考虑使用更高精度的IMU传感器
4. 添加温度补偿机制
