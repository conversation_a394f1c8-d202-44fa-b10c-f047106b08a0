/*
 * 测试Debug模式下速度漂移修复的简单验证程序
 * 编译命令: gcc -o test_debug_mode test_debug_mode.c -lm
 */

#include <stdio.h>
#include <math.h>
#include <stdlib.h>

// 模拟常量定义
#define TH_acc                  (5*0.542*0.001)
#define TH_gyr                  (5*0.013)
#define TH_acc_DEBUG            (2*0.542*0.001)
#define TH_gyr_DEBUG            (2*0.013)
#define E_SIM_MODE_NORMAL       0
#define E_SIM_MODE_DEBUG        1
#define RETURN_SUCESS           1
#define RETURN_FAIL             0

// 模拟结构体
typedef struct {
    int sim;
} Param_t;

typedef struct {
    double vn[3];
    double eb[3];  // 陀螺仪零偏
} SINS_t;

typedef struct {
    double Mgyr[3];  // 陀螺仪均值
} NAV_Data_t;

typedef struct {
    Param_t Param;
    SINS_t SINS;
    NAV_Data_t data;
    int ZUPT_flag;
} CombineData_t;

// 全局变量
CombineData_t combineData;

// 模拟ZUPT检测函数
int test_zupt_detection(double acc_std, double gyr_std, int debug_mode)
{
    double th_acc_use = TH_acc;
    double th_gyr_use = TH_gyr;
    
    if(debug_mode == E_SIM_MODE_DEBUG)
    {
        th_acc_use = TH_acc_DEBUG;
        th_gyr_use = TH_gyr_DEBUG;
    }
    
    printf("使用阈值: acc=%.6f, gyr=%.6f\n", th_acc_use, th_gyr_use);
    printf("实际值: acc_std=%.6f, gyr_std=%.6f\n", acc_std, gyr_std);
    
    if (sqrt(acc_std) < th_acc_use && sqrt(gyr_std) < th_gyr_use)
    {
        return RETURN_SUCESS;
    }
    else
    {
        return RETURN_FAIL;
    }
}

// 模拟速度约束函数
void test_velocity_constraint(double *vn, int debug_mode)
{
    if(debug_mode != E_SIM_MODE_DEBUG) return;
    
    double vel_magnitude = sqrt(vn[0]*vn[0] + vn[1]*vn[1] + vn[2]*vn[2]);
    
    printf("速度幅值: %.6f m/s\n", vel_magnitude);
    
    if(vel_magnitude > 5.0)
    {
        printf("速度过大，进行限制\n");
        double scale_factor = 1.0 / vel_magnitude;
        vn[0] *= scale_factor;
        vn[1] *= scale_factor;
        vn[2] *= scale_factor;
        printf("限制后速度: [%.6f, %.6f, %.6f]\n", vn[0], vn[1], vn[2]);
    }
}

// 模拟陀螺仪零偏学习函数
void test_gyro_bias_learning(double *eb, double *Mgyr, int zupt_flag, int debug_mode)
{
    if(!zupt_flag || debug_mode != E_SIM_MODE_DEBUG) return;
    
    double learning_rate = 0.001;
    printf("陀螺仪零偏学习，学习率: %.3f\n", learning_rate);
    printf("学习前零偏: [%.6f, %.6f, %.6f]\n", eb[0], eb[1], eb[2]);
    
    eb[0] = (1.0 - learning_rate) * eb[0] + learning_rate * Mgyr[0];
    eb[1] = (1.0 - learning_rate) * eb[1] + learning_rate * Mgyr[1];
    eb[2] = (1.0 - learning_rate) * eb[2] + learning_rate * Mgyr[2];
    
    printf("学习后零偏: [%.6f, %.6f, %.6f]\n", eb[0], eb[1], eb[2]);
}

// 测试函数
void run_tests()
{
    printf("=== Debug模式速度漂移修复测试 ===\n\n");
    
    // 测试1: ZUPT阈值检测
    printf("测试1: ZUPT阈值检测\n");
    printf("--- 正常模式 ---\n");
    int result1 = test_zupt_detection(0.001, 0.01, E_SIM_MODE_NORMAL);
    printf("检测结果: %s\n", result1 ? "静止" : "运动");
    
    printf("--- Debug模式 ---\n");
    int result2 = test_zupt_detection(0.001, 0.01, E_SIM_MODE_DEBUG);
    printf("检测结果: %s\n", result2 ? "静止" : "运动");
    printf("\n");
    
    // 测试2: 速度约束
    printf("测试2: 速度约束\n");
    double vn[3] = {3.48206, 0.0244141, -0.0183105};
    printf("原始速度: [%.6f, %.6f, %.6f]\n", vn[0], vn[1], vn[2]);
    test_velocity_constraint(vn, E_SIM_MODE_DEBUG);
    printf("\n");
    
    // 测试3: 陀螺仪零偏学习
    printf("测试3: 陀螺仪零偏学习\n");
    double eb[3] = {0.001, 0.002, 0.003};
    double Mgyr[3] = {0.0015, 0.0025, 0.0035};
    test_gyro_bias_learning(eb, Mgyr, RETURN_SUCESS, E_SIM_MODE_DEBUG);
    printf("\n");
    
    // 测试4: 强制速度为零
    printf("测试4: 强制速度为零\n");
    double vn_test[3] = {1.0, 2.0, 3.0};
    printf("强制前速度: [%.6f, %.6f, %.6f]\n", vn_test[0], vn_test[1], vn_test[2]);
    
    // 模拟debug模式下的强制速度为零
    if(E_SIM_MODE_DEBUG)
    {
        vn_test[0] = 0.0;
        vn_test[1] = 0.0;
        vn_test[2] = 0.0;
        printf("强制后速度: [%.6f, %.6f, %.6f]\n", vn_test[0], vn_test[1], vn_test[2]);
    }
    printf("\n");
    
    printf("=== 测试完成 ===\n");
}

int main()
{
    run_tests();
    return 0;
}
