# 完整导航系统代码架构解析

## 🏗️ 系统总体架构

### 硬件平台
- **主控芯片**: GD32F4xx (ARM Cortex-M4)
- **协处理器**: FPGA (数据预处理)
- **传感器**: IMU (MEMS/光纤陀螺)、GNSS、轮速计
- **通信接口**: CAN、UART、SPI、以太网

### 软件架构层次
```
┌─────────────────────────────────────────┐
│              应用层 (Application)        │
├─────────────────────────────────────────┤
│              算法层 (Algorithm)          │
├─────────────────────────────────────────┤
│              驱动层 (BSP/Driver)         │
├─────────────────────────────────────────┤
│              硬件层 (Hardware)           │
└─────────────────────────────────────────┘
```

## 📁 目录结构分析

### 1. NAV/ - 导航算法核心模块
```
NAV/
├── nav_app.c/h          # 导航应用主控制
├── nav_sins.c/h         # 捷联惯导算法
├── nav_kf.c/h           # 卡尔曼滤波
├── nav_imu.c/h          # IMU数据处理
├── nav_gnss.c/h         # GNSS数据处理
├── nav_ods.c/h          # 轮速计处理
├── nav_mahony.c/h       # Mahony姿态融合
├── nav_math.c/h         # 数学库函数
├── nav_type.h           # 数据类型定义
└── nav_const.h          # 常量定义
```

### 2. Source/ - 系统源码
```
Source/
├── inc/                 # 头文件
│   ├── INS_Data.h       # 导航数据结构
│   ├── INS_Sys.h        # 系统控制
│   ├── gnss.h           # GNSS接口
│   └── can_data.h       # CAN数据结构
├── src/                 # 源文件
│   ├── main.c           # 主程序入口
│   ├── INS_Data.c       # 数据管理
│   ├── gnss.c           # GNSS处理
│   └── can_data.c       # CAN数据处理
```

### 3. bsp/ - 板级支持包
```
bsp/
├── inc/                 # BSP头文件
├── src/                 # BSP源文件
│   ├── bsp_can.c        # CAN驱动
│   ├── bsp_uart.c       # UART驱动
│   ├── bsp_spi.c        # SPI驱动
│   └── bsp_gpio.c       # GPIO驱动
```

### 4. Protocol/ - 通信协议
```
Protocol/
├── computerFrameParse.c # 上位机协议解析
├── SetParaBao.c         # 参数设置
└── logger.c             # 数据记录
```

## 🔄 系统数据流

### 主数据流向
```
FPGA → IMU数据 → SINS算法 → 卡尔曼滤波 → 导航输出
  ↓      ↓         ↓          ↓           ↓
传感器  预处理   姿态解算   状态估计    结果输出
  ↑      ↑         ↑          ↑           ↑
GNSS → 位置速度 → 观测更新 → 误差校正 → 精度提升
轮速 → 约束信息 → 零速检测 → 漂移抑制 → 稳定性
```

### 核心数据结构
```c
// 主导航数据结构
typedef struct _NAV_Data_Full_t {
    _NAV_SINS_t     SINS;        // 捷联惯导
    _NAV_KF_t       KF;          // 卡尔曼滤波
    _NAV_IMU_t      IMU;         // IMU数据
    _NAV_GPS_t      GPS;         // GNSS数据
    _NAV_ODS_t      ODS;         // 轮速计数据
    _NAV_MAHONY_t   MAHONY;      // 姿态融合
    // ... 其他模块
} _NAV_Data_Full_t;
```

## 🧮 核心算法模块

### 1. SINS (捷联惯导) - nav_sins.c
```c
// 主要功能
- 姿态更新 (四元数/欧拉角)
- 速度积分
- 位置积分
- 重力补偿
- 地球自转补偿

// 关键函数
void SINS_Update(_NAV_Data_Full_t* NAV_Data_Full_p);
void att_update(_NAV_Data_Full_t* NAV_Data_Full_p);
void vel_update(_NAV_Data_Full_t* NAV_Data_Full_p);
void pos_update(_NAV_Data_Full_t* NAV_Data_Full_p);
```

### 2. 卡尔曼滤波 - nav_kf.c
```c
// 状态向量 (21维)
- 位置误差 (3)
- 速度误差 (3) 
- 姿态误差 (3)
- 陀螺仪零偏 (3)
- 加速度计零偏 (3)
- 比例因子 (6)

// 观测类型
- GNSS位置/速度
- ZUPT零速更新
- 轮速计约束
- 姿态约束

// 关键函数
void KF_UP2(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p);
void KF_Predict(_NAV_Data_Full_t* NAV_Data_Full_p);
void KF_Update_GPS(_NAV_Data_Full_t* NAV_Data_Full_p);
void KF_Update_ZUPT(_NAV_Data_Full_t* NAV_Data_Full_p);
```

### 3. IMU处理 - nav_imu.c
```c
// 主要功能
- 原始数据获取
- 温度补偿
- 零偏校正
- 比例因子校正
- 坐标系转换
- ZUPT检测

// 关键函数
void Get_IMU_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p);
void ZUPT_Detector(_NAV_Data_Full_t* NAV_Data_Full_p);
```

### 4. GNSS处理 - nav_gnss.c
```c
// 主要功能
- NMEA协议解析
- RTK状态监控
- 位置速度提取
- 精度评估
- 数据有效性检查

// 关键函数
void Get_GNSS_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p);
void GNSS_Data_Parse(GNSSDataTypeDef* gnss_data);
```

### 5. 轮速计处理 - nav_ods.c
```c
// 主要功能
- CAN数据接收
- 轮速优化算法
- 比例因子标定
- 静止检测
- 约束生成

// 关键函数
void Get_ODS_Data(_NAV_Data_Full_t* NAV_Data_Full_p, CombineDataTypeDef* CombineData_p);
double WheelSpeedOptimize(double v0, double v1, double v2, double v3, double scale_factor, unsigned char *valid_flag);
void ODS_Angle_Estimation(_NAV_Data_Full_t* NAV_Data_Full_p);
```

## 🔧 系统控制流程

### 主程序流程 (main.c)
```c
int main(void) {
    // 1. 系统初始化
    INS_Init();
    ReadParaFromFlash();
    
    // 2. 算法初始化
    NAV_Data_Full.Nav_Status = E_NAV_STATUS_START;
    
    // 3. 主循环
    while(1) {
        if(fpga_syn) {
            fpga_syn = 0;
            get_fpgadata();           // 获取FPGA数据
            wheel_is_running();       // 轮速状态检查
            
            if(combineData.gnssInfo.gpssecond != 0) {
                NAV_function();       // 导航解算
            }
            NAV_Output();            // 结果输出
        }
    }
}
```

### 导航状态机 (nav_app.c)
```c
// 导航系统状态
typedef enum {
    E_NAV_STATUS_START = 0,           // 系统启动
    E_NAV_STATUS_SINS_KF_INITIAL,     // SINS/KF初始化
    E_NAV_STATUS_SYSTEM_STANDARD,     // 系统标定
    E_NAV_STATUS_IN_NAV,              // 正常导航
    E_NAV_STATUS_STOP                 // 停止导航
} E_NAV_STATUS;

// 状态转换逻辑
void NAV_function(void) {
    switch(NAV_Data_Full.Nav_Status) {
        case E_NAV_STATUS_START:
            Param_Data_Init(&NAV_Data_Full);
            Load_Standard_Data(&NAV_Data_Full, &combineData);
            SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
            break;
            
        case E_NAV_STATUS_SINS_KF_INITIAL:
            SINS_Init(&NAV_Data_Full);
            if(NAV_Data_Full.SINS.Init_flag == 1) {
                SetNavStatus(E_NAV_STATUS_IN_NAV);
            }
            break;
            
        case E_NAV_STATUS_IN_NAV:
            NavKalmanMode(g_NavIndex);
            break;
    }
}
```

## 📊 关键参数配置

### 系统参数 (combineData.Param)
```c
typedef struct {
    float gnssArmLength[3];           // GNSS杆臂
    float gnssAtt_from_vehicle[3];    // 安装角度
    unsigned char methodType;         // 算法类型
    unsigned char sim;                // 调试模式
    unsigned int lostepoch;           // 失锁历元
    unsigned int HP;                  // 高精度模式
} Param_t;
```

### 滤波参数
```c
// 过程噪声
#define Q_gyro_bias     (1.0e-10)    // 陀螺零偏
#define Q_acc_bias      (1.0e-8)     // 加速度零偏
#define Q_att           (1.0e-6)     // 姿态噪声

// 观测噪声  
#define R_GPS_pos       (5.0*5.0)    // GPS位置
#define R_GPS_vel       (0.1*0.1)    // GPS速度
#define R_ZUPT          (0.02*0.02)  // ZUPT约束
```

## 🔌 接口与通信

### CAN接口 (bsp_can.c)
```c
// 轮速数据接收
void CAN0_RX0_IRQHandler(void) {
    can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
    
    // 解析轮速数据
    if(CAN_ID_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid) {
        // 前轮速度解析
        combineData.canInfo.data.WheelSpeed_Front_Left = ...;
        combineData.canInfo.data.WheelSpeed_Front_Right = ...;
    }
}
```

### UART接口
```c
// GNSS数据接收 (UART4)
// 调试输出 (UART0)  
// 用户通信 (UART6)
```

### 以太网接口 (TCPServer.c)
```c
// TCP服务器
// 数据上传
// 参数配置
```

## 🎛️ 详细模块分析

### 1. 数据获取模块

#### FPGA数据接口 (fpgad.c)
```c
// FPGA同步信号处理
extern volatile uint8_t fpga_syn;

void get_fpgadata(void) {
    // 从FPGA读取预处理后的IMU数据
    // 数据包含：加速度、角速度、温度
    // 已完成初步滤波和校准
}

// FPGA中断处理
void EXTI5_9_IRQHandler(void) {
    if(RESET != exti_interrupt_flag_get(EXTI_5)) {
        fpga_syn = 1;  // 设置同步标志
        exti_interrupt_flag_clear(EXTI_5);
    }
}
```

#### IMU数据类型支持
```c
// 支持多种IMU类型
typedef enum {
    IMU_TYPE_SCHA634 = 1,     // Murata SCHA634
    IMU_TYPE_ADIS16465 = 2,   // ADI ADIS16465
    IMU_TYPE_EPSON = 3,       // Epson G3xx系列
    IMU_TYPE_IFOG = 4         // 光纤陀螺
} IMU_TYPE_ENUM;

// 数据结构适配
typedef struct {
    scha634_real_data    scha634Info;
    adis16465_real_data  adis16465Info;
    epson_real_data      epsonInfo;
    IFOG_PARSE_DATA_TypeDef ifogInfo;
} CombineDataTypeDef;
```

### 2. 算法执行模块

#### 算法模式选择
```c
typedef enum {
    E_METHOD_TYPE_KALMAN = 0,  // 卡尔曼滤波模式
    E_METHOD_TYPE_DR = 1       // 航位推算模式
} E_METHOD_TYPE;

// 模式执行
if(E_METHOD_TYPE_KALMAN == combineData.Param.methodType) {
    NavKalmanMode(g_NavIndex);  // 卡尔曼滤波
} else {
    NavDRMode(g_NavIndex);      // 航位推算
}
```

#### 融合策略
```c
typedef enum {
    E_FUNSION_GPS = 0,          // GPS融合
    E_FUNSION_MOTION = 1,       // 运动约束
    E_FUNSION_WHEEL = 2,        // 轮速约束
    E_FUNSION_NO_GPS = 3        // 无GPS模式
} E_FUNSION_TYPE;
```

### 3. 输出控制模块

#### 数据输出管理 (INS_Output.c)
```c
// 输出数据结构
typedef struct {
    double timestamp;
    double latitude;
    double longitude;
    double altitude;
    double roll;
    double pitch;
    double heading;
    double ve, vn, vu;          // 东北天速度
    unsigned char status;
} NAV_Output_Data_t;

// 输出格式控制
typedef enum {
    OUTPUT_FORMAT_NMEA = 0,     // NMEA格式
    OUTPUT_FORMAT_BINARY = 1,   // 二进制格式
    OUTPUT_FORMAT_CUSTOM = 2    // 自定义格式
} OUTPUT_FORMAT_ENUM;
```

#### 串口输出配置
```c
typedef struct {
    uint32_t baudRate;          // 波特率
    uint8_t dataBits;           // 数据位
    uint8_t stopBits;           // 停止位
    uint8_t parity;             // 校验位
    uint8_t outputRate;         // 输出频率
    uint8_t outputFormat;       // 输出格式
} INS_Frame_Setting_TypeDef;

// 支持3个串口独立配置
INS_Frame_Setting_TypeDef serialFrameSetting[3];
```

### 4. 参数管理模块

#### Flash参数存储 (SetParaBao.c)
```c
// 参数存储结构
typedef struct {
    uint16_t firstProgram;      // 首次烧写标志
    uint16_t ProductID;         // 产品ID
    uint16_t DeviceID;          // 设备ID
    uint32_t ChipID[3];         // 芯片ID

    // 导航参数
    float gnssArmLength[3];     // GNSS杆臂
    float gnssAtt_from_vehicle[3]; // 安装角度
    float courseAngleCompensation; // 航向角补偿

    // 系统配置
    INS_Frame_Setting_TypeDef serialFrameSetting[3];
    INS_DATA_ENUMTypeDef datamode;
    INS_BOOT_MODE_ENUMTypeDef workmode;
} setting_t;

// 参数读写函数
void ReadParaFromFlash(void);
void WriteParaToFlash(void);
```

### 5. 通信协议模块

#### 上位机协议 (computerFrameParse.c)
```c
// 协议帧结构
typedef struct {
    uint8_t header[2];          // 帧头 0xAA 0x55
    uint8_t length;             // 数据长度
    uint8_t cmd;                // 命令字
    uint8_t data[256];          // 数据域
    uint8_t checksum;           // 校验和
} Protocol_Frame_t;

// 命令类型
#define CMD_GET_VERSION     0x01    // 获取版本
#define CMD_SET_PARAM       0x02    // 设置参数
#define CMD_GET_PARAM       0x03    // 获取参数
#define CMD_SAVE_PARAM      0x04    // 保存参数
#define CMD_RESET_SYSTEM    0x05    // 系统复位
```

#### 网络通信 (TCPServer.c)
```c
// TCP服务器配置
#define TCP_SERVER_PORT     8080
#define MAX_CLIENT_NUM      5

// 网络数据包
typedef struct {
    uint32_t timestamp;
    uint8_t dataType;
    uint16_t dataLength;
    uint8_t data[1024];
} Network_Packet_t;
```

### 6. 系统监控模块

#### 状态监控
```c
// 系统状态
typedef enum {
    SYS_STATUS_INIT = 0,        // 初始化
    SYS_STATUS_NORMAL = 1,      // 正常运行
    SYS_STATUS_WARNING = 2,     // 警告状态
    SYS_STATUS_ERROR = 3        // 错误状态
} SYS_STATUS_ENUM;

// 错误代码
#define ERR_IMU_TIMEOUT     0x01    // IMU超时
#define ERR_GNSS_TIMEOUT    0x02    // GNSS超时
#define ERR_CAN_TIMEOUT     0x04    // CAN超时
#define ERR_TEMP_ABNORMAL   0x08    // 温度异常
```

#### 数据记录 (logger.c)
```c
// 日志级别
typedef enum {
    LOG_LEVEL_DEBUG = 0,
    LOG_LEVEL_INFO = 1,
    LOG_LEVEL_WARNING = 2,
    LOG_LEVEL_ERROR = 3
} LOG_LEVEL_ENUM;

// 日志记录
void writeCSVLog(char* filename, LogBufTypeDef* pBuf);
void generateCSVLogFileName(char* filename);
```

## 🔄 系统时序分析

### 主循环时序 (200Hz)
```
每5ms执行一次：
├── FPGA同步信号检测
├── IMU数据获取 (1ms)
├── GNSS数据检查 (1ms)
├── 导航算法执行 (2ms)
│   ├── SINS更新
│   ├── 卡尔曼滤波
│   └── 状态估计
└── 结果输出 (1ms)
```

### 数据更新频率
- **IMU数据**: 200Hz (FPGA同步)
- **GNSS数据**: 1-10Hz (异步)
- **轮速数据**: 50Hz (CAN总线)
- **导航输出**: 200Hz (可配置)

## 🛡️ 系统可靠性设计

### 1. 看门狗保护
```c
// 独立看门狗
void fwdgt_config(void) {
    fwdgt_config(FWDGT_PSC_DIV64, 0x0FFF);  // 4秒超时
    fwdgt_enable();
}

// 定期喂狗
void fwdgt_counter_reload(void);
```

### 2. 异常处理
```c
// 硬件异常处理
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);

// 软件异常恢复
void SystemErrorRecovery(uint32_t errorCode);
```

### 3. 数据完整性检查
```c
// CRC校验
uint16_t CRC16_Calculate(uint8_t* data, uint16_t length);

// 数据范围检查
bool DataRangeCheck(double value, double min, double max);

// 时间戳检查
bool TimestampCheck(uint32_t timestamp);
```

这个导航系统是一个完整的GNSS/INS组合导航解决方案，采用模块化设计，支持多种传感器融合和通信接口，具有完善的可靠性保护机制。
