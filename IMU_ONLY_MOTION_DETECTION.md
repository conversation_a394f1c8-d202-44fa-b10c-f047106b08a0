# 基于纯IMU的运动状态检测方案

## 🎯 应用场景

**设备配置**：不接轮速计，仅依靠IMU进行运动状态检测
**核心挑战**：在GNSS失锁时，准确区分静止和运动状态

## 🔍 纯IMU运动检测策略

### 1. IMU数据特征分析

#### 静止状态特征
- **加速度幅值**：接近重力加速度(9.8m/s²)，变化很小
- **角速度幅值**：接近零，只有传感器噪声
- **数据稳定性**：连续多个周期保持稳定

#### 运动状态特征  
- **加速度幅值**：明显偏离重力加速度，有动态变化
- **角速度幅值**：有明显的转动信号
- **数据变化**：存在明显的动态特性

### 2. 检测阈值设计

#### 保守阈值策略
```c
// 静止检测阈值（严格）
#define ACC_STATIONARY_THRESHOLD    0.25    // 0.25 m/s²
#define GYRO_STATIONARY_THRESHOLD   0.04    // 0.04 rad/s (约2.3°/s)
#define VEL_STATIONARY_THRESHOLD    0.2     // 0.2 m/s

// 运动检测阈值（宽松）
#define ACC_MOVING_THRESHOLD        0.6     // 0.6 m/s²
#define GYRO_MOVING_THRESHOLD       0.1     // 0.1 rad/s (约5.7°/s)
```

#### 阈值选择原理
- **静止阈值严格**：避免误判运动为静止
- **运动阈值宽松**：确保真正的运动能被检测到
- **中间区域**：状态不确定，采用温和约束

### 3. 连续确认机制

```c
// 静止状态需要连续确认，避免瞬时误判
static int stationary_confirm_counter = 0;
bool imu_indicates_stationary = (acc_magnitude < 0.3) && (gyro_magnitude < 0.05);

if(imu_indicates_stationary && current_vel_mag < 0.2) {
    stationary_confirm_counter++;
    // 需要连续20个周期确认静止（约1秒）
    if(stationary_confirm_counter >= 20) {
        // 确实静止时才启用ZUPT约束
        NAV_Data_Full.ZUPT_flag = RETURN_SUCESS;
    }
} else {
    // 检测到运动，立即重置计数器
    stationary_confirm_counter = 0;
}
```

## 🛠️ 实现方案

### 1. 应用层检测 (nav_app.c)

```c
// 基于IMU数据的智能运动状态检测
// 不依赖轮速计，纯粹通过IMU判断真实运动状态

// 1. 计算IMU数据的幅值
double acc_magnitude = sqrt(acc_use[0]² + acc_use[1]² + acc_use[2]²);
double gyro_magnitude = sqrt(gyro_use[0]² + gyro_use[1]² + gyro_use[2]²);

// 2. 计算当前速度幅值作为辅助判断
double current_vel_mag = sqrt(vn[0]² + vn[1]² + vn[2]²);

// 3. 基于IMU数据的静止状态判断
static int stationary_confirm_counter = 0;
bool imu_indicates_stationary = (acc_magnitude < 0.3) && (gyro_magnitude < 0.05);

if(imu_indicates_stationary && current_vel_mag < 0.2) {
    stationary_confirm_counter++;
    if(stationary_confirm_counter >= 20) { // 连续确认
        NAV_Data_Full.ZUPT_flag = RETURN_SUCESS;
    }
} else {
    stationary_confirm_counter = 0; // 重置计数器
}
```

### 2. 差异化约束策略 (nav_app.c)

```c
// 纯IMU运动状态判断
bool likely_moving = (acc_magnitude > 0.8) || (gyro_magnitude > 0.15);
bool likely_stationary = (acc_magnitude < 0.2) && (gyro_magnitude < 0.03) && (current_vel_mag < 0.3);

// 基于运动状态的差异化处理
if(likely_stationary) {
    // 确认静止：应用约束帮助收敛
    if(current_vel_mag > 0.05) {
        double decay_factor = 0.9; // 较强衰减
        vn[0] *= decay_factor; vn[1] *= decay_factor; vn[2] *= decay_factor;
    }
    if(current_vel_mag < 0.01) {
        vn[0] = vn[1] = vn[2] = 0.0; // 置零
    }
}
else if(likely_moving) {
    // 确认运动：只做极端发散保护
    if(current_vel_mag > 30.0) { // 30m/s = 108km/h
        double scale_factor = 20.0 / current_vel_mag;
        vn[0] *= scale_factor; vn[1] *= scale_factor; vn[2] *= scale_factor;
    }
}
else {
    // 状态不确定：温和约束
    if(current_vel_mag > 8.0) {
        double scale_factor = 5.0 / current_vel_mag;
        vn[0] *= scale_factor; vn[1] *= scale_factor; vn[2] *= scale_factor;
    }
}
```

### 3. 卡尔曼滤波层约束 (nav_kf.c)

```c
// 基于IMU的运动状态检测（不依赖轮速计）
double acc_magnitude = sqrt(acc_use[0]² + acc_use[1]² + acc_use[2]²);
double gyro_magnitude = sqrt(gyro_use[0]² + gyro_use[1]² + gyro_use[2]²);

// 纯IMU运动状态判断
bool likely_moving = (acc_magnitude > 0.6) || (gyro_magnitude > 0.1);
bool likely_stationary = (acc_magnitude < 0.25) && (gyro_magnitude < 0.04) && (vel_norm < 0.2);

// 基于运动状态的差异化约束策略
if(likely_stationary && ZUPT_flag == RETURN_SUCESS) {
    // 确认静止状态：应用强约束
    if(vel_norm > 0.1) {
        double decay_factor = 0.8; // 强衰减
        vn[0] *= decay_factor; vn[1] *= decay_factor; vn[2] *= decay_factor;
    }
    if(vel_norm < 0.05) {
        vn[0] = vn[1] = vn[2] = 0.0; // 置零
    }
}
else if(likely_moving) {
    // 确认运动状态：只做发散保护
    if(vel_norm > 10.0) {
        double limit_factor = 5.0 / vel_norm;
        vn[0] *= limit_factor; vn[1] *= limit_factor; vn[2] *= limit_factor;
    }
}
```

## 📊 阈值参数对比

| 检测类型 | 加速度阈值 | 角速度阈值 | 速度阈值 | 用途 |
|----------|------------|------------|----------|------|
| **严格静止** | <0.2 m/s² | <0.03 rad/s | <0.3 m/s | 强约束触发 |
| **一般静止** | <0.25 m/s² | <0.04 rad/s | <0.2 m/s | ZUPT触发 |
| **轻微运动** | 0.25-0.6 m/s² | 0.04-0.1 rad/s | 0.2-0.8 m/s | 温和约束 |
| **明显运动** | >0.6 m/s² | >0.1 rad/s | >0.8 m/s | 轻约束 |
| **强烈运动** | >0.8 m/s² | >0.15 rad/s | >2.0 m/s | 仅发散保护 |

## 🎯 关键优势

### 1. 无外部依赖
- **纯IMU检测**：不依赖轮速计、GNSS等外部传感器
- **自主判断**：基于设备自身的运动特征
- **实时响应**：无需等待外部数据

### 2. 多层次保护
- **连续确认**：避免瞬时误判
- **差异化约束**：根据运动状态调整策略
- **渐进处理**：平滑的状态转换

### 3. 鲁棒性强
- **保守策略**：宁可少约束，不误约束
- **多重验证**：加速度+角速度+速度估计
- **自适应调整**：根据实际情况动态调整

## 🧪 测试验证要点

### 静止测试
1. **设备完全静止**：应该快速检测到静止状态
2. **轻微振动**：不应误判为运动
3. **长期静止**：速度应收敛到接近零

### 动态测试
1. **缓慢启动**：应该及时检测到运动开始
2. **正常行驶**：不应被误判为静止
3. **转弯制动**：应该正确识别动态过程

### 边界测试
1. **静止到运动**：状态转换应该平滑
2. **运动到静止**：应该有适当的延迟确认
3. **频繁变化**：系统应该稳定响应

## 💡 核心创新点

1. **纯IMU方案**：完全不依赖外部传感器
2. **连续确认机制**：避免瞬时误判
3. **差异化约束**：根据运动状态智能调整
4. **保守策略**：优先保证动态性能

这个方案专门针对不接轮速计的应用场景设计，通过纯IMU数据实现可靠的运动状态检测和差异化约束策略。
